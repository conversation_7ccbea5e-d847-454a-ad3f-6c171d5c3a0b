import React, { useEffect, useState } from 'react';
import i18n, { t } from 'i18next';
import { Form, Input, Modal, Tooltip } from 'antd';
import Icon from '@/components/Icon';
import './index.scss';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import { Lang } from '@/config/type';

interface FormLableProps {
  title: string;
  tips?: string;
}
const FormLable: React.FC<FormLableProps> = (props: FormLableProps) => {
  return (
    <span>
      <span>{props.title} </span>
      {props.tips && (
        <Tooltip title={props.tips}>
          <Icon type="iconexplain"></Icon>
        </Tooltip>
      )}
    </span>
  );
};

export interface EditPanelModalProps {
  open: boolean;
  editInfo: any;
  isEdit: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
}
const EditPanelModal: React.FC<EditPanelModalProps> = (props: EditPanelModalProps) => {
  const { open, onCancel, onOk, editInfo, isEdit } = props;
  const [form] = Form.useForm();
  const [baseInfo, setBaseInfo] = useState<any>();
  const [mfLang, setMfLang] = useState<Lang>(); // mf的原因多了一层，否则不用，有form name就够了

  useEffect(() => {
    form.resetFields();
    form.setFieldsValue(editInfo);
    setBaseInfo(editInfo);
    setMfLang(editInfo?.lang ?? {});
  }, [editInfo]);

  const handleOk = async () => {
    try {
      await form.validateFields();
      const formValue = form.getFieldsValue();
      onOk({ ...baseInfo, ...formValue });
    } catch (error) {
      // Form validation failed, error messages will be displayed by the form
      console.log('Form validation failed:', error);
    }
  };

  const handleTitleChange = (data: any) => {
    form.setFieldValue(['lang', 'title'], data);
    form.setFieldValue(['title'], data[i18n?.language ?? 'zh_CN']);
    setMfLang({ ...mfLang, title: data });
  };

  return (
    <>
      <Modal
        className="edit-panel-modal"
        title={isEdit ? t('dj-编辑操作') : t('dj-添加操作')}
        open={open}
        width={400}
        okText={t('dj-确定')}
        onOk={handleOk}
        cancelText={t('dj-取消')}
        onCancel={onCancel}
      >
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} layout={'horizontal'} form={form}>
          <div className="edit-content-fields">
            <Form.Item name={['title']} hidden={true}>
              <Input />
            </Form.Item>
            <Form.Item
              name={['lang', 'title']}
              rules={[
                { required: true, message: '请输入标题' },
                {
                  validator: (_, value) => {
                    if (!value || !value.zh_CN || !value.zh_TW || !value.en_US) {
                      return Promise.reject(new Error('请输入所有语言的标题'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
              label={<FormLable title={t('dj-标题')} tips="title"></FormLable>}
              colon={false}
            >
              <AthenaDesignerCoreMFComponent
                componentName="AppLangInput"
                componentProps={{
                  className: 'ath-lang-input',
                  size: 'small',
                  title: t('dj-标题'),
                  onChange: handleTitleChange,
                  required: true,
                  value: mfLang?.title ?? {}, // mf的原因多了一层，否则不用，有form name就够了
                  placeholder: t('dj-请输入'),
                }}
              />
            </Form.Item>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default EditPanelModal;
