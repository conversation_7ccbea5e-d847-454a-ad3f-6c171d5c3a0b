import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  ViewChild,
  TemplateRef,
  ChangeDetectorRef,
} from '@angular/core';
import { DataEntryWorkDesignService } from './service/data-entry-work-design.service';
import { DataEntryWorkDesignRequestService } from './service/data-entry-work-design-request.service';
import { TranslateService } from '@ngx-translate/core';
import { historyModalProps, pageTypeNameMap } from './config/data-entry-work-design.config';
import { isEqual, cloneDeep } from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription, Subject } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';
import { AppService } from '../../../../pages/apps/app.service';
import { BusinessShareInfo } from '../../../bussiness-components/business-share-consumer/type';
import { BusinessShareConsumerMFComponent } from '../../../bussiness-components/business-share-consumer/business-share-consumer-mf.component';
import { DesignerTypes } from 'app/types';
import { AdUserService } from 'pages/login/service/user.service';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { getDataConnectorByAction } from './utils/tools';

@Component({
  selector: 'app-data-entry-work-design',
  templateUrl: './data-entry-work-design.component.html',
  styleUrls: ['./data-entry-work-design.component.less'],
  providers: [DataEntryWorkDesignService, DataEntryWorkDesignRequestService],
})
export class DataEntryWorkDesignComponent implements OnInit, OnChanges, OnDestroy {
  @Input() modelPageType: 'design' | 'browse' | 'edit';
  @Input() pageDesignCode: string; // 作业code

  @Input() isHideHeaderRight: boolean = false; // 是否隐藏头部右侧操作
  @Input() headerCustomTemplate: TemplateRef<any> | null = null; // 模型驱动解决方案自定义头部，之前处理这块的逻辑的开发者将headerCustomTemplate放在workData里面，参与了workData的更新，性能堪忧，现在将其优化拆分出去
  @Output() close: EventEmitter<any> = new EventEmitter();
  @Output() saveAndPublishLoadingChange: EventEmitter<boolean> = new EventEmitter(); // 当保存和发布状态变化时触发的事件，配合优化需求，保存和发布时模型驱动解决方案左侧菜单不可点击
  @Output() contentChangeWithoutSaveChange: EventEmitter<boolean> = new EventEmitter(); // 内容变更未保存状态的变化

  historyModalProps: HistoryModalProps = cloneDeep(historyModalProps); // 操作记录弹窗信息
  showCodeModal: boolean; // 代码界面是否打开
  codeData: any = {}; // 代码界面的代码数据

  workDataHasChanged: boolean = false; // 关闭时提醒

  destroy$ = new Subject();
  saveAndPublishLoadingChange$: Subscription; // 保存和发布loading状态变化

  initPageData: {} = null; // 每个页面formio第一次完整渲染之后的数据对象（配合检测数据变更使用）

  dynamicWorkDesignBusinessShareInfo: BusinessShareInfo = null; // 使用子应用的共享业务组件

  designerType = `${DesignerTypes.WORK}@${this.userService.getUser('branch')}`;
  gobalErrorMessage: string = ''; // 数据录入错误信息，当不为空时，代表数据录入页面存在问题

  actionModalVisiable: boolean = false; // action 开窗
  private importDataConnectorsPromiseHandle: {
    // 导入数据源promise
    resolve: (value: any) => void;
    reject: (reason?: any) => void;
  } | null = null;

  @ViewChild('dynamicWorkDesign') dynamicWorkDesign: BusinessShareConsumerMFComponent;

  constructor(
    public dataEntryWorkDesignService: DataEntryWorkDesignService,
    private dataEntryWorkDesignRequestService: DataEntryWorkDesignRequestService,
    private athMessageService: NzMessageService,
    private translateService: TranslateService,
    private appService: AppService,
    private userService: AdUserService,
    private modal: AdModalService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.dataEntryWorkDesignService.setSaveLoading(true);
    this.saveAndPublishLoadingChange$ = this.dataEntryWorkDesignService.saveLoadingChange$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isLoading: boolean) => {
        this.saveAndPublishLoadingChange.emit(isLoading);
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('pageDesignCode')) {
      this.handleInit();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // 初始化
  private async handleInit(): Promise<void> {
    this.dataEntryWorkDesignService.setInitInfoLoading(true);
    try {
      this.dataEntryWorkDesignService.setPageDesignCode(this.pageDesignCode);
      this.dataEntryWorkDesignService.setPageType(this.modelPageType);

      const initPageDesignInfoRes = await this.initPageDesignInfo();
      this.dataEntryWorkDesignService.setPageDesignInfo(initPageDesignInfoRes.data);

      const getAppPageDesignsRes = await this.getAppPageDesigns();
      this.dataEntryWorkDesignService.setAppPageDesigns(getAppPageDesignsRes.data);

      this.updateRenderInfo();
    } catch (error) {
      this.gobalErrorMessage = error?.error?.errorMessage ?? error?.message ?? 'init error';
      console.log('handleInit error:', error);
    } finally {
      this.dataEntryWorkDesignService.setInitInfoLoading(false);
    }
  }

  // 初始化界面设计数据信息
  initPageDesignInfo(): Promise<any> {
    const params = {
      code: this.dataEntryWorkDesignService.pageDesignCode,
      application: this.appService.selectedApp.code,
    };
    return this.dataEntryWorkDesignRequestService.queryPageDesign(params).toPromise();
  }

  // 更新渲染数据
  updateRenderInfo(): void {
    this.handleInitDynamicWorkDesignBussinessInfo();
  }

  // 保存
  async handleSave(): Promise<void> {
    try {
      this.dataEntryWorkDesignService.setSaveLoading(true);
      await this.savePageDesign();
      this.athMessageService.success(this.translateService.instant('dj-保存成功'));
      this.initPageData = this.getInitPageData();
      this.judgeDataChange();
    } catch (error) {
      console.error('handleSave error:', error);
    } finally {
      this.dataEntryWorkDesignService.setSaveLoading(false);
    }
  }

  async savePageDesign(): Promise<any> {
    // 保存时剔除系统变量
    return this.dataEntryWorkDesignRequestService
      .savePageDesign(
        this.dataEntryWorkDesignService.pageDslList.map((pageDslInfo) => {
          return {
            ...pageDslInfo,
            dsl: {
              ...pageDslInfo.dsl,
              variables: pageDslInfo.dsl.variables.filter((variable) => {
                return variable.scope !== 'system';
              }),
            },
          };
        }),
      )
      .toPromise();
  }

  async singlePublish(): Promise<any> {
    return this.dataEntryWorkDesignRequestService
      .singlePublish({
        code: this.dataEntryWorkDesignService.pageDesignCode,
        application: this.appService.selectedApp.code,
      })
      .toPromise();
  }

  async getAppPageDesigns(): Promise<any> {
    return this.dataEntryWorkDesignRequestService
      .getAppPageDesigns({
        application: this.appService.selectedApp.code,
      })
      .toPromise();
  }

  async queryEspActionFields(actionId): Promise<any> {
    return this.dataEntryWorkDesignRequestService
      .queryEspActionFields({
        actionId,
      })
      .toPromise();
  }

  // 打开代码开窗
  openCodeModal(): void {
    this.codeData = cloneDeep(this.dataEntryWorkDesignService?.activePageDslInfo?.dsl ?? {});
    // 剔除系统变量
    this.codeData = {
      ...this.codeData,
      variables: this.codeData.variables?.filter((variable) => {
        return variable.scope !== 'system';
      }),
    };
    this.showCodeModal = true;
  }

  handleCodeModal(data: any): void {
    const { variables, ...otherdDslData } = data;
    this.showCodeModal = false;
    const systemVariables = (this.dataEntryWorkDesignService?.activePageDslInfo?.dsl ?? {}).variables.filter(
      (variable) => {
        return variable.scope === 'system';
      },
    );
    this.dataEntryWorkDesignService.setActivePageDslInfoDsl({
      ...otherdDslData,
      variables: [...variables, ...systemVariables],
    });
    this.updateRenderInfo();
  }

  openModifyHistoryModal(): void {
    this.historyModalProps.code = this.dataEntryWorkDesignService.pageDesignCode;
    this.historyModalProps.transferModal = true;
  }

  // close时判断页面数据是否变化
  judgeDataChange() {
    if (!!this.initPageData) {
      const currentPageData = this.getInitPageData();
      this.contentChangeWithoutSaveChange.emit(!isEqual(this.initPageData, currentPageData));
    }
  }

  // 渲染数据发生变化
  handleChangePage(): void {
    if (!this.initPageData) this.initPageData = this.getInitPageData();
    this.judgeDataChange();
  }

  // 处理了用户点击了发布
  async handlePublic() {
    try {
      this.dataEntryWorkDesignService.setSaveLoading(true);
      await this.savePageDesign();
      const singlePublishRes = await this.singlePublish();
      this.dataEntryWorkDesignService.setPageDesignInfoByKeyPath(['publishedTime'], singlePublishRes.data);
      this.athMessageService.success(this.translateService.instant('dj-发布成功'));
      this.initPageData = this.getInitPageData();
      this.judgeDataChange();
    } catch (error) {
      console.error('handlePublic error:', error);
    } finally {
      this.dataEntryWorkDesignService.setSaveLoading(false);
    }
  }

  // 获取用于对比数据变更的初始化作业数据
  getInitPageData() {
    return {
      ...cloneDeep(this.dataEntryWorkDesignService.pageDesignInfo),
    };
  }

  handleInitDynamicWorkDesignBussinessInfo() {
    const pageUIElementContent = this.dataEntryWorkDesignService?.activePageDslInfo?.dsl;

    const pageList =
      this.dataEntryWorkDesignService?.appPageDesigns?.map((item) => ({
        title: item.name,
        lang: {
          title: item?.lang?.name ?? {},
        },
        children: item.pageDslList.map((page) => ({
          ...pageTypeNameMap[page.type],
          value: page.code,
        })),
      })) ?? [];

    // 定义事件处理策略
    const dispatchEventHandlers = {
      // 数据源导入
      importDataConnectors: this.handleImportDataConnectors.bind(this),
    };

    this.dynamicWorkDesignBusinessShareInfo = {
      componentType: 'DynamicWorkDesign', // 组件类型
      componentProps: {
        version: '2.0',
        dynamicWorkDesignInfo: {
          config: {},
          dynamicWorkDesignConfig: {},
        },
        dynamicWorkDesignRenderData: {
          pageUIElementContent: {
            ...pageUIElementContent,
            variables: [
              ...(pageUIElementContent?.variables ?? []),
              {
                name: 'sys_page_list',
                defaultValue: [...pageList],
                scope: 'system',
              },
            ],
          },
        },
        changeDynamicWorkDesignRenderData: (data) => {
          console.log('changeDynamicWorkDesignRenderData:', data);
          const { pageUIElementContent, dataSourceNames, dataSources, iamCondition, groupSchemaList = [] } = data;
          this.dataEntryWorkDesignService.setActivePageDslInfoDsl(pageUIElementContent);
          this.handleChangePage();
        },
        changeDynamicWorkDesignStatus: (data) => {
          console.log('changeDynamicWorkDesignStatus:', data);
          this.dataEntryWorkDesignService.setSaveLoading(data !== 'Ready');
          this.cd.detectChanges();
        },
        dispatch: (eventType: string, data: any, callback?: (result: any) => void) => {
          console.log(data);
          // 调用事件处理策略
          const handler = dispatchEventHandlers[eventType];
          if (handler) {
            handler(data, callback);
          } else {
            console.warn(`未处理的事件类型: ${eventType}`);
          }
        },
      },
    };
  }

  async handleImportDataConnectors(data: any, callback?: (result: any) => void): Promise<void> {
    this.handleOpenActionModal();
    try {
      const result = await new Promise<any>((resolve, reject) => {
        this.importDataConnectorsPromiseHandle = { resolve, reject };
      });
      callback?.(result);
    } catch (error) {
      console.error(error);
      this.athMessageService.error(this.translateService.instant('dj-导入失败'));
      callback?.(null);
    } finally {
      this.importDataConnectorsPromiseHandle = null;
    }
  }

  handleOpenActionModal() {
    this.actionModalVisiable = true;
  }

  handleCloseActionModal() {
    this.actionModalVisiable = false;
    if (this.importDataConnectorsPromiseHandle) this.importDataConnectorsPromiseHandle.resolve(null);
  }

  async handleSelectActionToDataConnectors(actionInfo: {
    actionAllData: {
      actionId: string;
      serviceName: string;
      provider: string;
    };
    [propName: string]: any;
  }) {
    this.actionModalVisiable = false;
    if (!this.importDataConnectorsPromiseHandle) {
      console.log('无需导入数据源');
      return;
    }
    const { resolve, reject } = this.importDataConnectorsPromiseHandle;
    this.dataEntryWorkDesignService.setSaveLoading(true);
    try {
      const { actionAllData } = actionInfo;
      const { actionId } = actionAllData;
      const queryEspActionFieldsRes = await this.queryEspActionFields(actionId);
      console.log('handleSelectAction:', getDataConnectorByAction(actionAllData, queryEspActionFieldsRes.data));
      const dataConnector = getDataConnectorByAction(actionAllData, queryEspActionFieldsRes.data);
      if (!!dataConnector) {
        resolve(dataConnector);
        return;
      }
      reject(new Error('转换失败'));
    } catch (error) {
      console.log('handleSelectAction error:', error);
      reject(error);
    } finally {
      this.dataEntryWorkDesignService.setSaveLoading(false);
    }
  }
}
