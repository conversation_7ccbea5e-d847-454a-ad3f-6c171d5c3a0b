import { PageType } from './data-entry-work-design.type';

// 历史记录的弹窗数据信息
export const historyModalProps: HistoryModalProps = {
  transferModal: false,
  code: '',
  collection: 'pageDesign',
};

export const pageTypeNameMap = {
  browse: {
    title: '浏览界面',
    lang: {
      title: {
        zh_CN: '浏览界面',
        zh_TW: '浏览界面',
        en_US: 'Browse Page',
      },
    },
  },
  edit: {
    title: '编辑界面',
    lang: {
      title: {
        zh_CN: '编辑界面',
        zh_TW: '编辑界面',
        en_US: 'Edit Page',
      },
    },
  },
  design: {
    title: '界面设计',
    lang: {
      title: {
        zh_CN: '界面设计',
        zh_TW: '界面设计',
        en_US: 'Page Design',
      },
    },
  },
};

export const dataConnectorTemplate = {
  id: '', // 内部使用,32位UUID
  name: 'data_name', // 使用名，唯一，取fieldData的data_name
  description: '用于创建新用户的API', // 取fieldData的description，支持多语言
  connectType: 'api', // 链接类型，目前仅有API
  runOnPageLoad: true, // 页面加载时立即请求数据，这里我们默认传true
  option: {
    request: {
      method: 'POST',
      path: '{{variables.system.ipaasURL}}',
      params: [],
      body: {
        type: 'json',
        content: {
          std_data: {
            parameter: {
              enterprise_no: '{{variables.system.sys_c_ent}}',
              site_no: '{{variables.system.sys_c_org}}',
              is_digiwin_product: 'Y',
            },
          },
        },
      },
      headers: [
        {
          key: 'Content-Type',
          value: 'application/json; charset=utf-8',
        },
        {
          key: 'digi-protocol',
          value: 'raw',
        },
        {
          key: 'digi-type',
          value: 'sync',
        },
        {
          key: 'digi-host', // timestamp为空，后面在脚本中替换
          value: JSON.stringify({
            prod: '{{variables.system.sys_fx_prod}}',
            ip: '{{variables.system.sys_fx_ip}}',
            timestamp: '',
            acct: '{{variables.system.sys_c_user}}}',
          }),
        },
        {
          key: 'digi-service',
          value: {
            prod: '环境变量-digi-service-prod', // 来自于接口返回的数据
            name: '环境变量-digi-service-name', // 来自于接口返回的数据
            ip: '{{variables.system.sys_T100_ip}}',
            id: '{{variables.system.sys_T100_id}}',
            UID: '{{variables.system.sys_T100_uid}}',
          },
        },
        {
          key: 'digi-datakey',
          value: JSON.stringify({
            EntId: '{{variables.system.sys_c_ent}}',
            CompanyId: '{{variables.system.sys_c_org}}',
          }),
        },
      ],
      cookies: [],
    },
    preProcess: {
      type: 'javascript',
      script: `
                const { headers, ...rest} = request;
                const digi_host = headers['digi-host'];
                const digi_service = headers['digi-service'];
                digi_host.timestamp = new Date().getTime(); 
                const digi_key = {
                  "key": "digi-key",
                  "value": CryptoJS.MD5(JSON.stringify(digi_host)+JSON.stringify(digi_service)).toString()
                }
                headers['digi_key'] = digi_key;
                return {
                  ...rest,
                  headers
                }
              `,
    },
    postProcess: {
      type: 'javascript',
      script: null,
    },
    // 仅设计时自用
    response: {
      type: 'Esp', // 默认Standard：标准树结构，Esp：原ESP数据字段结构
      meta: {}, // 后台返回的字段树数据
    },
  },
};
