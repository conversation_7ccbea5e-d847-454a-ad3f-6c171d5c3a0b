
// export * from './request'
// 页面dsl类型
export interface IDataDsl {
    layout?: [], // 页面代码
    hooks?: [], // hooks
    rules?: [],
    variables?: [], // 用户变量/系统变量
    dataConnectors?: IDataConnector[], // 数据源
    globalSetting?: {}, // 页面定义
}

// 数据源type
export interface IDataConnector {
    id?: string; // 内部使用,32位UUID
    name?: string; // 使用名，唯一
    description?: string;
    connectType?: string; // 链接类型，目前仅有API
    runOnPageLoad?: boolean; // 页面加载时立即请求数据
    runOnlyIf?: string; // 页面加载时立即请求数据
    option?: IDataConnectorOption;
    connectorType?: string;
    connectorId?: string;
}

type methods = 'GET' | 'POST' | 'PUT' | 'DELETE'
interface IDataConnectorOption {
    request?: {
        method?: methods,
        path?: string,
        params?: IParams[]
        body?: IBody
        headers?: IParams[]
        cookies?: []
    }
    preProcess?: {
        type: string;
        script: string
    }
    postProcess?: {
        type: string;
        script: string
    }
    response?:  {
        type: responseType, // "Standard | Esp"
        meta: ISchemaType | ITreeSchemaType
    }
    responseTree?: ITreeSchemaType
}


type responseType = 'Standard' | 'Esp'

interface IParams {
    key: string;
    value: string;
    description: string
    id?: string
    [props: string]: unknown
}
interface IBody {
    type: string,
    content: string
}
export interface ISchemaType {
    id?: string;
    description?: string;
    name?: string
    dataType?: string
    children?: ISchemaType[]
}
export interface ITreeSchemaType {
    fullPath?: string;
    id?: string;
    data_name?: string
    data_type?: string
    is_array?:boolean
    description?: {
        en_US: string
        zh_CN: string
        zh_TW: string
    }
    children?: ISchemaType[],
    field?: ITreeSchemaType[]
    desc?: string
    name?: string
}
// --- 数据源类型定义结束

// 数据源组件props
export interface DataConnectorsProps {
    dataConnectors?: IDataConnector[]; // 根据实际类型补充
    onChange?: (data: IDataConnector[]) => void; // 根据实际类型补充
}

export interface DataSearchProps {
    onInputChange: (value: string) => void; // 实时输入回调（防抖后）
    onAdd: () => void;                      // 点击 add icon 回调
    onImport: () => void;                   // 点击 import icon 回调
    placeholder?: string;                   // 可选，输入框占位符
}

// 数据项类型定义
export interface DataConnectorsItemType {
    id?: string;
    name: string;
    connectType: string
    description?: string;
    runOnPageLoad?: boolean;
    runOnlyIf?: string;
    option?: {
        request?: {
            method?: methods,
            path?: string,
            params?: IParams[]
            body?: IBody
            headers?: IParams[]
            cookies?: []
        }
        preProcess?: {
            type: string;
            script: string
        }
        postProcess?: {
            type: string;
            script: string
        }
        response?: ISchemaType
        responseTree?: ITreeSchemaType
    };
}




// 数据源抽屉组件的props
export interface DataConnectorEditorProps {
    visible: boolean;
    currentItem?: DataConnectorsItemType;
    onClose: () => void;
    onSave: (data: DataConnectorsItemType) => void;
    dataConnectorNameMap?: Map<string, string>;
}


// 数据源配置表单的props
export interface ConfigFormLayoutProps {
    data?: DataConnectorFormData
    onBaseInfoFormChange?: (values: BaseInfoFormData) => void;
    onParamsChange?: (values: ParamsTabsData) => void;
    onResponseForm?: (values: ISchemaType[]) => void;
    onRequestApi?: () => void;
}
export interface DataConnectorFormData {
    baseInfo?: BaseInfoFormData,
    paramsInfo?: ParamsTabsData
    responseInfo?: ISchemaType[]
}

interface ParamsTabsData {
    params?: IParams[],
    body?: IBody
    headers?: IParams[],
    advanced?:IadvancedSettingForm
    preEvent?: IScript
}
export interface ParamsTabsProps {
    data: ParamsTabsData
    onChange?: (item) => void;
}


// 数据预览组件的props
export interface PreviewDataProps {
    data?: any[];
    loading?: boolean;
}
interface BaseInfoFormData {
    name?: string
    method?: methods
    path?: string
}

export interface BaseInfoFormProps {
    data?: BaseInfoFormData;
    onChange?: (item: BaseInfoFormData) => void;
    onRequestApi: () => void
}

// 数据列表组件 props
export interface DataConnectorsListProps {
    dataList: DataConnectorsItemType[];
    onEdit?: (item: DataConnectorsItemType) => void;
    onDelete?: (id: string) => void;
    onCopy?: (item: DataConnectorsItemType) => void;
}


// 数据项组件 props
export interface DataConnectorsItemProps {
    data: DataConnectorsItemType;
    index: number;
    onEdit?: (data: DataConnectorsItemType) => void;
    onDelete?: (id: string) => void;
    onCopy?: (data: DataConnectorsItemType) => void;
}

// Tree 节点数据类型
export interface TreeNodeType {
    id?: string;
    title?: string;
    name?: string;
    key?:string
    desc?: string;
    children?: TreeNodeType[];
    field?: TreeNodeType[];
    dataType?: string;  // 字段类型，如 string, number 等
}

// Tree 组件 props
export interface DataConnectorsTreeProps {
    treeData: TreeNodeType[];
    onSelect?: (selectedKeys: string[], info: any) => void;
    onExpand?: (expandedKeys: string[]) => void;
    defaultExpandedKeys?: string[];
    defaultSelectedKeys?: string[];
    dataSourceName?: string;
}
interface IadvancedSettingForm {
    timeout?: number
    retryCount?: number
    runOnPageLoad?: boolean
    runOnlyIf?: string;
}
export interface AdvancedSettingFormProps {
    data: IadvancedSettingForm
    onChange?: (value: IadvancedSettingForm) => void
}

export interface EditableTableProps {
    value: IParams[],
    onChange?: (value: IParams[]) => void
    fields?: Array<'key' | 'type' | 'value' | 'description'>,
    typeOptions?: Array<{ label: string; value: string }>
    [props: string]: unknown
    leftTitle?: string;
}
interface IScript {
    preProcess: string,
    postProcess: string
}
export interface IScriptEditorProps {
    value: IScript
    onChange: (data: IScript) => void
}
interface INestedEditableData {
    name?: string
    dataType?: string
    description?: string
    children?: INestedEditableData[]
}
export interface NestedEditableTableWrapperProps {
    value: INestedEditableData,
    onChange: (items) => void
}
export interface NestedEditableTableProps {
    dataSource: NestedEditableRow[];
    onChange: (data: NestedEditableRow[], type?:string) => void;
    editingRowId: string | null;
    setEditingRowId: (id: string | null) => void;
    showHeader?: boolean;
    onAdd: (item) => void,
    onBlur: () => void,
    onFieldChange: (item) => void,
    errors: Record<string, string>
  }
  export interface NestedEditableRow {
    id: string;
    name?: string;
    dataType?: string;
    description?: string;
    children?: NestedEditableRow[];
    [key: string]: any;
  }
  
