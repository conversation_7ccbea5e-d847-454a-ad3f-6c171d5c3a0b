import { v4 as uuidv4 } from 'uuid';
import { chain, cloneDeep, isEmpty, isNil } from 'lodash';
import { ISchemaType, ITreeSchemaType } from '../types/index';
import type { IDataConnector, DataConnectorsItemType } from '../types/index';

/**
 * 递归为 schema 增加 id
 */
export function addIdToSchemaTree(node: ISchemaType): ISchemaType {
  if (!node) return node;
  const id = node.id || uuidv4();
  // 只有原本有 children 字段时才递归
  if (Array.isArray(node.children)) {
    return {
      ...node,
      id,
      children: node.children.map((child) => addIdToSchemaTree(child)),
    };
  }
  // 没有 children 字段时，不加 children
  const { children, ...rest } = node;
  return { ...rest, id };
}
/**
 * 递归转换 ISchemaType（树结构）为 ITreeSchemaType（树结构）
 * @param node ISchemaType
 */
export function convertResponseTree(node, parentFullPath?): ITreeSchemaType {
  if (!node) return null;
  const fullPath = parentFullPath ? `${parentFullPath}.${node.name}` : node.name;
  const path = parentFullPath ? parentFullPath : '';
  const isArray = node.dataType === 'array';
  const children = node.children
    ? node.children.map((child) => convertResponseTree(child, fullPath))
    : null;
  return {
    ...node,
    data_name: node.name,
    data_type: node.dataType,
    fullPath,
    // title: node.name,
    is_array: isArray,
    field: children,
    children,
    path,
    description: {
      en_US: '',
      zh_CN: node?.description || '',
      zh_TW: '',
    },
    desc: node?.description || '',
  };
}

function treeSchemaToISchemaType(tree): ISchemaType {
  if (!tree) return {} as ISchemaType;
  return {
    id: tree.id || uuidv4(),
    name: tree.data_name || tree.name,
    dataType: tree.data_type || tree.dataType,
    description:
      typeof tree.description === 'string'
        ? tree.description
        : tree.description?.zh_CN || tree.description?.en_US || tree.description?.zh_TW || '',
    children: tree?.children?.length
      ? tree.children
      : tree.field?.map(treeSchemaToISchemaType) || [],
  };
}

function convertResponseToStandardData(response) {
  if (!response) return response;
  if (response.type === 'Esp') {
    const newMeta = isTreeSchemaType(response.meta)
      ? treeSchemaToISchemaType(response.meta)
      : response.meta;
    if (!isEmpty(newMeta)) newMeta['isDataBody'] = true;
    return {
      dataType: 'object',
      children: [newMeta],
    };
  } else {
    return addIdToSchemaTree(response.meta);
  }
}

function isTreeSchemaType(obj: any): boolean {
  return obj && (obj.data_name !== undefined || obj.data_type !== undefined);
}

// 数据源外部数据转内部格式
export function externalToInternal(dataConnectors): DataConnectorsItemType[] {
  return (dataConnectors || []).map((connector) => {
    const newConnector = { ...connector };
    let response = newConnector.option?.response;
    let responseTree = newConnector.option?.responseTree;
    response = convertResponseToStandardData(response);
    // 只在没有 responseTree 时才转化
    if (response && (isNil(responseTree) || isEmpty(responseTree))) {
      responseTree = buildResponseTree(response, newConnector.name);
    }
    // 转给数据源子组件的数据，将meta抹去，onChange时转换出meta
    return {
      ...newConnector,
      option: {
        ...newConnector.option,
        response, // 带 id 的原始 response
        responseTree, // 转换后的树结构
      },
    };
  });
}

function buildResponseTree(response, connectorName) {
    let markedData = findMarkedItem(response);
    if (!markedData) {
        markedData = response
    }
    // todo--称与接口名称保持一致
    if (markedData && markedData.name !== connectorName) {
        markedData.name = connectorName
    }
    // if (treeData && !treeData.name) {
    //     treeData.name = connectorName
    // }
    let responseTree = convertResponseTree(markedData);
    return responseTree;
}

// 数据源组件内部格式转外部（onChange 出口用）
export function internalToExternal(dataConnectors): IDataConnector[] {
  return (dataConnectors || []).map((connector) => {
    const newConnector = { ...connector };
    // id 兜底
    newConnector.id = newConnector.id || uuidv4();
    // option/response 处理
    if (newConnector.option && newConnector.option.response) {
      // 处理 response
      const response = addIdToSchemaTree(newConnector.option.response);
      // 处理 responseTree
      const responseTree = buildResponseTree(response, newConnector.name);
      newConnector.option = {
        ...newConnector.option,
        response: {
          type: 'Standard',
          meta: response,
        },
        responseTree,
      };
    }

    return newConnector;
  });
}

export function getAllKeyTreeData(treeDatas): string[] {
  if (!treeDatas || treeDatas.length === 0) return [];
  let stack = cloneDeep(treeDatas);
  const allKeyList: string[] = [];
  while (stack.length > 0) {
    const node = stack.shift();
    allKeyList.push(node.key);
    if (node.children && node.children.length > 0) {
      stack = stack.concat(node.children);
    }
  }
  return allKeyList;
}
/**
 * 递归校验对象及其嵌套属性
 * @param {object|array} data - 需要校验的数据
 * @param {Array<{key: string, tips: string}>} requiredFields - 必填项配置
 * @returns {boolean} 校验通过返回true，否则false
 */
export function validateNestedFields({data, requiredFields, message, t}) {
  if (Array.isArray(data)) {
    for (const item of data) {
      if (!validateNestedFields({data:item, requiredFields, message, t})) {
        return false;
      }
    }
  } else if (typeof data === 'object' && data !== null) {
    for (const field of requiredFields) {
      if (Object.prototype.hasOwnProperty.call(data, field.key)) {
        const value = data[field.key];
        if (
          value === undefined ||
          value === null ||
          value === '' ||
          (typeof value === 'string' && value.trim() === '')
        ) {
          message.error(t(field.tips));
          return false;
        }
      }
    }
    // 递归校验所有属性
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        if (typeof data[key] === 'object' && data[key] !== null) {
          if (!validateNestedFields({data:data[key], requiredFields, message,t})) {
            return false;
          }
        }
      }
    }
  }
  return true;
}
function findMarkedItem(tree) {
  if (!tree) return undefined;
  if (tree.isDataBody) {
    return {...tree};
  }
  if (Array.isArray(tree.children)) {
    for (const node of tree.children) {
      const found = findMarkedItem(node);
      if (found) return found;
    }
  }
  return undefined;
}
