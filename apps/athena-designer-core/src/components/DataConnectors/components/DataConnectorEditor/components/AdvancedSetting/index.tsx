import React from 'react';
import { Form, Input, Switch } from 'antd';
import {AdvancedSettingFormProps} from '../../../../types';
import { useTranslation } from 'react-i18next';
import './index.less'

const AdvancedSettingForm: React.FC<AdvancedSettingFormProps> = ({ data, onChange }) => {
    const { t } = useTranslation();

    const handleChange = (formData) => {
        const newValue = { ...data, ...formData };
        onChange?.(newValue);
    };

    return (
        <Form className='set-box'>
            {/* <Form.Item label={t('dj-超时时长（毫秒）')}>
                <Input value={data.timeout} onChange={e => handleChange({ timeout: e.target.value })} />
            </Form.Item>
            <Form.Item label={t('dj-重试次数')}>
                <Input value={data.retryCount} onChange={e => handleChange({ retryCount: e.target.value })} />
            </Form.Item> */}
            <Form.Item label={t('dj-页面加载时立即请求数据')}>
                <input className='set-box-input' value={data.runOnlyIf} onChange={e => handleChange({ runOnlyIf: e.target.value })} />
                
                {/* <Switch className='set-box-switch'  value={data.runOnPageLoad} onChange={runOnPageLoad => handleChange({ runOnPageLoad })} /> */}
            </Form.Item>
        </Form>
    );
};

export default AdvancedSettingForm;