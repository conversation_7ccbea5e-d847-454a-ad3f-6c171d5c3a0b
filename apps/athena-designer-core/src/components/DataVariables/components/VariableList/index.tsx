import React, { useState } from "react";
import { Input, Divider, Tooltip } from "antd";
import { RightOutlined, UpOutlined, DownOutlined, SearchOutlined } from '@ant-design/icons'
import type { VariableTreeNode, VariableItem } from "../../types";
import { useTranslation } from 'react-i18next';
import './index.less'



interface Props {
    variableList: VariableTreeNode[];
    selectedId?: string;
    onSelect: (id: string) => void;
    onSearch: (e) => void;
}
interface itemProps {
    variableItem: VariableTreeNode
    actionType: string
    onSelect: (item) => void
}
const VariableItem: React.FC<itemProps> = ({ variableItem, onSelect, actionType }) => {
    const { t } = useTranslation()
    const [showMore, setShowMore] = useState(false)
    const displayData = showMore ? variableItem.children : variableItem.children.slice(0, 3);
    return (
        <div className="variable-list-box" style={{ borderColor: actionType === variableItem?.type ? '#605CE5' : '#FAFAFC' }} onClick={() => {
            onSelect(variableItem)
        }}>
            <div className="header">
                <div className="header-left">{t(variableItem.name)}</div>
                <div className="header-right">
                    <RightOutlined />
                </div>
            </div>
            {
                variableItem?.children?.length ? (
                    <div className="content">
                        {
                            displayData.map((item) => {
                                return <div key={item.id} className="content-title">
                                    {item.name}
                                    {item.defaultValue ? `(${item.defaultValue})` : ''}
                                </div>
                                {/* <div key={item.id} className="content-title">{`${highlight(item.name, searchKeyword)}(${item.defaultValue})`} </div> */ }
                                // return <Tooltip  key={item.id} title={item.defaultValue} overlayClassName="variable-default-value-tooltip">
                                //     <div  className="content-title">
                                //         {item.name}
                                //         {item.defaultValue ? `(${item.defaultValue})` : ''}
                                //     </div>
                                // </Tooltip>

                            })
                        }

                    </div>
                ) : <div className="empty-tips">{t('dj-暂无数据')}</div>
            }
            {
                variableItem?.children?.length > 3 ? (
                    <div className='bottom'>
                        <Divider style={{ borderColor: '#E6E6EB' }} dashed>
                            <div className="collapse-button" onClick={e => {
                                e.stopPropagation()
                                setShowMore(prev => !prev)
                            }}>
                                <span className="collapse-button-text">{showMore ? t('dj-收起') : t('dj-展开')}</span>
                                {showMore ? <UpOutlined /> : <DownOutlined />}
                            </div>
                        </Divider>
                    </div>
                ) : null
            }



        </div>
    )
}

const VariableList: React.FC<Props> = ({ variableList, onSelect, onSearch }) => {
    const [actionType, setActionType] = useState('');
    return (
        <div className="variable-list">
            {/* <Input className="search" prefix={<SearchOutlined />} placeholder="搜索变量" onChange={onSearch} style={{ marginBottom: 8 }} /> */}
            {(variableList || []).filter(item => item.showGroupEmpty || item?.children?.length).map((item) => (
                <VariableItem
                    actionType={actionType}
                    key={item.type}
                    onSelect={(item) => {
                        setActionType(item?.type)
                        onSelect(item)
                    }}
                    variableItem={item}
                />
            ))}
        </div>
    );
};

export default VariableList;